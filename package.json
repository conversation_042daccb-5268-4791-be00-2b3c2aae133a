{"name": "mcp-embedding-search", "version": "0.0.0", "description": "Search embedding databases", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"mcp-embedding-search": "./dist/index.js"}, "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "tsc && chmod +x dist/index.js", "start": "node dist/index.js", "dev": "npx @modelcontextprotocol/inspector dist/index.js", "changeset": "changeset", "version": "changeset version", "release": "pnpm run build && changeset publish"}, "keywords": ["mcp", "model-context-protocol"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/spences10/mcp-embedding-search.git"}, "bugs": {"url": "https://github.com/spences10/mcp-embedding-search/issues"}, "homepage": "https://github.com/spences10/mcp-embedding-search#readme", "dependencies": {"@modelcontextprotocol/sdk": "1.15.0", "@libsql/client": "^0.15.7"}, "devDependencies": {"@changesets/cli": "^2.29.4", "@types/node": "^22.15.21", "typescript": "^5.8.3"}}